# Production Environment Configuration for BQJavaAPI

# Spring Configuration
spring:
  application:
    name: bq-java-api

# Server Configuration
server:
  port: 8081
      
# BigQuery Configuration - Production Environment
google:
  project:
    id: ${BQ_PROJECT_ID:your-prod-project-id}
  credentials:
    path: ${BQ_CREDENTIALS_PATH:/config/prod-gcp-credentials.json}

bigquery:
  dataset: ${BQ_DATASET:bq_transactions_prod}
  table: ${BQ_TABLE:aspn_data_prod}

# Processing Configuration
app:
  batch-size: 1000  # Full batch size for production
  concurrency: 20   # Maximum concurrency for production server
  
# Logging Configuration
logging:
  level:
    root: WARN
    com.example.bqjavaapi: INFO
    com.google.cloud.bigquery: ERROR
