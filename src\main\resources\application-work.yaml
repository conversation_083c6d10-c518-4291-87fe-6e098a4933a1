# Work Environment Configuration for BQJavaAPI

# Spring Configuration
spring:
  application:
    name: bq-java-api

# Server Configuration
server:
  port: 8082
      
# BigQuery Configuration - Work Setup
google:
  project:
    id: jovial-engine-458300-n6  # Replace with your work project ID if different
  credentials:
    path: ./config/gcp-credentials.json

bigquery:
  dataset: kafka_bq_transactions  # Replace with your work dataset name if different
  table: kafka_messages          # Replace with your work table name if different

# API Configuration
api:
  use-mock: false  # Set to false to use the real API endpoint
  base-url: http://myworkplace.com/rxdata  # Replace with your actual work API endpoint
  username: your-username  # Replace with your actual username
  password: your-password  # Replace with your actual password

# Processing Configuration
app:
  batch-size: 100  # Adjust based on your work environment needs
  concurrency: 5   # Adjust based on your work environment needs
  flush-interval-ms: 5000
  
# Detailed Logging Configuration
logging:
  level:
    root: INFO
    com.example.bqjavaapi: DEBUG
    com.google.cloud.bigquery: INFO
    com.google.api.client.http: INFO
    com.google.cloud: INFO
