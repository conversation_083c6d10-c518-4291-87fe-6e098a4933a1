# Application configuration
spring.application.name=bq-java-api

# BigQuery configuration
google.project.id=your-google-project-id
# google.credentials.path=/path/to/your/credentials.json
# If credentials path is not set, default application credentials will be used

# API configuration
api.base-url=https://your-api-endpoint.com/api

# Processing configuration
app.batch-size=1000
app.concurrency=10

# Logging
logging.level.root=INFO
logging.level.com.example.bqjavaapi=INFO
# Set to DEBUG for more detailed logs
# logging.level.com.example.bqjavaapi=DEBUG
