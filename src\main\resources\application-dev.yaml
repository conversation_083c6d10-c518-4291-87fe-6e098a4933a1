# Development Environment Configuration for BQJavaAPI

# Spring Configuration
spring:
  application:
    name: bq-java-api

# Server Configuration
server:
  port: 8081
      
# BigQuery Configuration - Development Environment
google:
  project:
    id: ${BQ_PROJECT_ID:your-dev-project-id}
  credentials:
    path: ${BQ_CREDENTIALS_PATH:/config/dev-gcp-credentials.json}

bigquery:
  dataset: ${BQ_DATASET:bq_transactions_dev}
  table: ${BQ_TABLE:aspn_data_dev}

# Processing Configuration
app:
  batch-size: 500  # Medium batch size for development
  concurrency: 8   # Medium concurrency for development server
  
# Logging Configuration
logging:
  level:
    root: INFO
    com.example.bqjavaapi: DEBUG
    com.google.cloud.bigquery: INFO
